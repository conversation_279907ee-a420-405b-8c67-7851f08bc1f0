{"name": "flat18", "version": "0.1.0", "private": true, "scripts": {"dev": "node scripts/copy-bootstrap-icons.js && next dev", "build": "node scripts/copy-bootstrap-icons.js && npm run optimize-images && NODE_ENV=production npm run optimize-css && next build && node scripts/fix-asset-paths.js", "build:gh-pages": "node scripts/copy-bootstrap-icons.js && npm run optimize-images && NODE_ENV=production npm run optimize-css && NEXT_DISABLE_ESLINT=1 next build && node scripts/fix-asset-paths.js", "start": "next start", "lint": "next lint", "export": "node scripts/copy-bootstrap-icons.js && npm run optimize-images && NODE_ENV=production npm run optimize-css && next build && node scripts/fix-asset-paths.js", "deploy": "node scripts/copy-bootstrap-icons.js && npm run optimize-images && NODE_ENV=production NEXT_DISABLE_ESLINT=1 next build && node scripts/fix-asset-paths.js && touch out/.nojekyll && cp public/_headers out/ && echo 'flat18.co.uk' > out/CNAME", "deploy:perf": "node scripts/copy-bootstrap-icons.js && npm run optimize-images && NODE_ENV=production npm run optimize-css && NEXT_DISABLE_ESLINT=1 next build && node scripts/fix-asset-paths.js && touch out/.nojekyll && cp public/_headers out/ && echo 'flat18.co.uk' > out/CNAME", "copy-icons": "node scripts/copy-bootstrap-icons.js", "purge-css": "node scripts/purge-css.js", "optimize-images": "node scripts/optimize-images.js", "optimize-css": "node scripts/optimize-css.js", "optimize-css:prod": "NODE_ENV=production node scripts/optimize-css.js", "optimize": "npm run optimize-images && npm run optimize-css:prod", "fix-assets": "node scripts/fix-asset-paths.js"}, "dependencies": {"bootstrap-icons": "^1.11.3", "form-data": "^4.0.2", "framer-motion": "^12.6.3", "mailgun.js": "^12.0.1", "next": "^14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "19.1.13", "autoprefixer": "^10.4.21", "critters": "^0.0.25", "csso": "^5.0.5", "eslint": "^8.57.1", "eslint-config-next": "^15.2.4", "glob": "^11.0.1", "postcss": "^8.5.3", "purgecss": "^7.0.2", "sharp": "^0.34.1", "tailwindcss": "^3.4.17"}}